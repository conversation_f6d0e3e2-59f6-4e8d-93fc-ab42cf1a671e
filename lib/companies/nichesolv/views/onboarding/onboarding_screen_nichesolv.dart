import 'package:flutter/material.dart';
import 'package:nds_app/screens/onboarding_templates/onboarding_template1.dart';

/// Nichesolv-specific onboarding screen
/// Uses OnboardingTemplate1 for standard onboarding behavior
class OnboardingScreenNichesolv extends OnboardingTemplate1 {
  const OnboardingScreenNichesolv({Key? key}) : super(key: key);

  @override
  State<OnboardingScreenNichesolv> createState() =>
      _OnboardingScreenNichesolvState();
}

class _OnboardingScreenNichesolvState
    extends OnboardingTemplate1State<OnboardingScreenNichesolv> {
  @override
  String getScreenName() => 'Nichesolv Onboarding Screen';
}
