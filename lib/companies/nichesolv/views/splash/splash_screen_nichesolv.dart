import 'package:flutter/material.dart';
import 'package:nds_app/screens/splash_templates/splash_template1.dart';

/// Nichesolv-specific splash screen (main splash)
/// Uses SplashTemplate1 for standard splash behavior
class SplashScreenNichesolv extends SplashTemplate1 {
  const SplashScreenNichesolv({Key? key}) : super(key: key);

  @override
  State<SplashScreenNichesolv> createState() => SplashScreenNichesolvState();
}

class SplashScreenNichesolvState
    extends SplashTemplate1State<SplashScreenNichesolv> {
  @override
  String getScreenName() => 'Nichesolv Splash Screen';
}
