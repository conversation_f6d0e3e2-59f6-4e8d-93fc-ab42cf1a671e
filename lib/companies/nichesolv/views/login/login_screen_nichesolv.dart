import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/login.dart';
import '../otp/otp_verification_screen_nichesolv.dart';

/// Nichesolv-specific login screen
/// Extends BaseLoginScreen with Nichesolv-specific navigation
class LoginScreenNichesolv extends BaseLoginScreen {
  const LoginScreenNichesolv({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreenNichesolv> createState() => _LoginScreenNichesolvState();
}

class _LoginScreenNichesolvState
    extends BaseLoginScreenState<LoginScreenNichesolv> {
  @override
  String getScreenName() => 'Nichesolv Login Screen';

  @override
  void navigateToOtpScreen(String phoneNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreenNichesolv(
          phoneNumber: phoneNumber,
          phoneNumText: mobileNumberController.text.trim(),
          settings: settings,
        ),
      ),
    );
  }
}
