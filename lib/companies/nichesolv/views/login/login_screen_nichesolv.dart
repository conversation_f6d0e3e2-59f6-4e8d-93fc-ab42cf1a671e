import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/login_templates/login_template2.dart';
import '../otp/otp_verification_screen_nichesolv.dart';

/// Nichesolv-specific login screen
/// Uses LoginTemplate2 for alternative login UI
class LoginScreenNichesolv extends LoginTemplate2 {
  const LoginScreenNichesolv({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreenNichesolv> createState() => _LoginScreenNichesolvState();
}

class _LoginScreenNichesolvState
    extends LoginTemplate2State<LoginScreenNichesolv> {
  @override
  String getScreenName() => 'Nichesolv Login Screen';

  @override
  void navigateToOtpScreen(String phoneNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreenNichesolv(
          phoneNumber: phoneNumber,
          phoneNumText: mobileNumberController.text.trim(),
          settings: settings,
        ),
      ),
    );
  }
}
