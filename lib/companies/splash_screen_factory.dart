import 'package:flutter/material.dart';
// B2C imports
import 'package:nds_app/companies/b2c/views/splash/splash_screen_b2c.dart';
import 'package:nds_app/companies/b2c/views/loading/loading_screen_b2c.dart';
import 'package:nds_app/companies/b2c/views/onboarding/onboarding_screen_b2c.dart';
import 'package:nds_app/companies/b2c/views/promotional/promotional_screen_b2c.dart';
// Lapa imports
import 'package:nds_app/companies/lapa/views/splash/splash_screen_lapa.dart';
import 'package:nds_app/companies/lapa/views/loading/loading_screen_lapa.dart';
import 'package:nds_app/companies/lapa/views/onboarding/onboarding_screen_lapa.dart';
import 'package:nds_app/companies/lapa/views/promotional/promotional_screen_lapa.dart';
// NDS imports
import 'package:nds_app/companies/nds/views/splash/splash_screen_nds.dart';
import 'package:nds_app/companies/nds/views/loading/loading_screen_nds.dart';
import 'package:nds_app/companies/nds/views/onboarding/onboarding_screen_nds.dart';
import 'package:nds_app/companies/nds/views/promotional/promotional_screen_nds.dart';
// ProdRed imports
import 'package:nds_app/companies/prodred/views/splash/splash_screen_prodred.dart';
import 'package:nds_app/companies/prodred/views/loading/loading_screen_prodred.dart';
import 'package:nds_app/companies/prodred/views/onboarding/onboarding_screen_prodred.dart';
import 'package:nds_app/companies/prodred/views/promotional/promotional_screen_prodred.dart';
// Nichesolv imports
import 'package:nds_app/companies/nichesolv/views/splash/splash_screen_nichesolv.dart';
import 'package:nds_app/companies/nichesolv/views/loading/loading_screen_nichesolv.dart';
import 'package:nds_app/companies/nichesolv/views/onboarding/onboarding_screen_nichesolv.dart';
import 'package:nds_app/companies/nichesolv/views/promotional/promotional_screen_nichesolv.dart';
import 'package:nds_app/companies/nichesolv/views/login/login_screen_nichesolv.dart';

/*// Login screen imports
import 'package:nds_app/companies/b2c/views/login/login_screen_b2c.dart';
import 'package:nds_app/companies/lapa/views/login/login_screen_lapa.dart';
import 'package:nds_app/companies/nds/views/login/login_screen_nds.dart';
import 'package:nds_app/companies/prodred/views/login/login_screen_prodred.dart';*/
// Branding
import '../branding/branding.dart';
import 'package:nds_app/models/user_activity_setting.dart';

/// Factory class to create company-specific splash screens based on the current company configuration
class SplashScreenFactory {
  /// Creates and returns the appropriate splash screen widget based on the company name
  /// The branding system automatically handles company-specific logos and assets
  static Widget createSplashScreen() {
    switch (companyName) {
      case 'b2c':
        return const SplashScreenB2C();
      case 'lapa':
        return const SplashScreenLapa();
      case 'nds':
        return const SplashScreenNDS();
      case 'prodred':
        return const SplashScreenProdRed();
      case 'Nichesolv':
        return const SplashScreenNichesolv();
      default:
        // Default to NDS if company is not recognized
        return const SplashScreenNDS();
    }
  }

  /// Creates and returns the appropriate loading screen widget based on the company name
  static Widget createLoadingScreen() {
    switch (companyName) {
      case 'b2c':
        return const LoadingScreenB2C();
      case 'lapa':
        return const LoadingScreenLapa();
      case 'nds':
        return const LoadingScreenNDS();
      case 'prodred':
        return const LoadingScreenProdRed();
      case 'Nichesolv':
        return const LoadingScreenNichesolv();
      default:
        // Default to NDS if company is not recognized
        return const LoadingScreenNDS();
    }
  }

  /// Creates and returns the appropriate onboarding screen widget based on the company name
  static Widget createOnboardingScreen() {
    switch (companyName) {
      case 'b2c':
        return const OnboardingScreenB2C();
      case 'lapa':
        return const OnboardingScreenLapa();
      case 'nds':
        return const OnboardingScreenNDS();
      case 'prodred':
        return const OnboardingScreenProdRed();
      case 'Nichesolv':
        return const OnboardingScreenNichesolv();
      default:
        // Default to NDS if company is not recognized
        return const OnboardingScreenNDS();
    }
  }

  /// Creates and returns the appropriate promotional screen widget based on the company name
  static Widget createPromotionalScreen() {
    switch (companyName) {
      case 'b2c':
        return const PromotionalScreenB2C();
      case 'lapa':
        return const PromotionalScreenLapa();
      case 'nds':
        return const PromotionalScreenNDS();
      case 'prodred':
        return const PromotionalScreenProdRed();
      case 'Nichesolv':
        return const PromotionalScreenNichesolv();
      default:
        // Default to NDS if company is not recognized
        return const PromotionalScreenNDS();
    }
  }

  /// Creates and returns the appropriate login screen widget based on the company name
  /*static Widget createLoginScreen(
      {required List<UserActivitySetting> settings}) {
    switch (companyName) {
      case 'b2c':
        return LoginScreenB2C(settings: settings);
      case 'lapa':
        return LoginScreenLapa(settings: settings);
      case 'nds':
        return LoginScreenNDS(settings: settings);
      case 'prodred':
        return LoginScreenProdRed(settings: settings);
      case 'Nichesolv':
        return LoginScreenNichesolv(settings: settings);
      default:
        // Default to NDS if company is not recognized
        return LoginScreenNDS(settings: settings);
    }
  }*/

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'Nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
