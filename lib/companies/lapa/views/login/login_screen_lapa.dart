import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/login_templates/login_template1.dart';
import '../otp/otp_verification_screen_lapa.dart';

/// Lapa-specific login screen
/// Uses LoginTemplate1 for standard login UI
class LoginScreenLapa extends LoginTemplate1 {
  const LoginScreenLapa({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreenLapa> createState() => _LoginScreenLapaState();
}

class _LoginScreenLapaState extends LoginTemplate1State<LoginScreenLapa> {
  @override
  String getScreenName() => 'Lapa Login Screen';

  @override
  void navigateToOtpScreen(String phoneNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreenLapa(
          phoneNumber: phoneNumber,
          phoneNumText: mobileNumberController.text.trim(),
          settings: settings,
        ),
      ),
    );
  }
}
