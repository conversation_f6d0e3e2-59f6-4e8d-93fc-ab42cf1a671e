import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/login.dart';
import '../otp/otp_verification_screen_lapa.dart';

/// Lapa-specific login screen
/// Extends BaseLoginScreen with Lapa-specific navigation
class LoginScreenLapa extends BaseLoginScreen {
  const LoginScreenLapa({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreenLapa> createState() => _LoginScreenLapaState();
}

class _LoginScreenLapaState extends BaseLoginScreenState<LoginScreenLapa> {
  @override
  String getScreenName() => 'Lapa Login Screen';

  @override
  void navigateToOtpScreen(String phoneNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreenLapa(
          phoneNumber: phoneNumber,
          phoneNumText: mobileNumberController.text.trim(),
          settings: settings,
        ),
      ),
    );
  }
}
