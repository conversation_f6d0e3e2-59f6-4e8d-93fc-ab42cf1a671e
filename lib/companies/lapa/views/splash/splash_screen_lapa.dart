import 'package:flutter/material.dart';
import 'package:nds_app/screens/splash_templates/splash_template1.dart';

/// Lapa-specific splash screen (main splash)
/// Uses SplashTemplate1 for standard splash behavior
class SplashScreenLapa extends SplashTemplate1 {
  const SplashScreenLapa({Key? key}) : super(key: key);

  @override
  State<SplashScreenLapa> createState() => SplashScreenLapaState();
}

class SplashScreenLapaState extends SplashTemplate1State<SplashScreenLapa> {
  @override
  String getScreenName() => 'Lapa Splash Screen';
}
