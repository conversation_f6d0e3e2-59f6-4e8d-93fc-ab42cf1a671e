import 'package:flutter/material.dart';
import 'package:nds_app/screens/onboarding_templates/onboarding_template1.dart';

/// Lapa-specific onboarding screen
/// Uses OnboardingTemplate1 for standard onboarding behavior
class OnboardingScreenLapa extends OnboardingTemplate1 {
  const OnboardingScreenLapa({Key? key}) : super(key: key);

  @override
  State<OnboardingScreenLapa> createState() => _OnboardingScreenLapaState();
}

class _OnboardingScreenLapaState
    extends OnboardingTemplate1State<OnboardingScreenLapa> {
  @override
  String getScreenName() => 'Lapa Onboarding Screen';
}
