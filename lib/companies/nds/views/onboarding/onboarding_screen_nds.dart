import 'package:flutter/material.dart';
import 'package:nds_app/screens/onboarding_templates/onboarding_template1.dart';

/// NDS-specific onboarding screen
/// Uses OnboardingTemplate1 for standard onboarding behavior
class OnboardingScreenNDS extends OnboardingTemplate1 {
  const OnboardingScreenNDS({Key? key}) : super(key: key);

  @override
  State<OnboardingScreenNDS> createState() => _OnboardingScreenNDSState();
}

class _OnboardingScreenNDSState
    extends OnboardingTemplate1State<OnboardingScreenNDS> {
  @override
  String getScreenName() => 'NDS Onboarding Screen';
}
