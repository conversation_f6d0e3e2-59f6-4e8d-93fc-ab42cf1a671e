import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/login.dart';
import '../otp/otp_verification_screen_nds.dart';

/// NDS-specific login screen
/// Extends BaseLoginScreen with NDS-specific navigation
class LoginScreenNDS extends BaseLoginScreen {
  const LoginScreenNDS({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreenNDS> createState() => _LoginScreenNDSState();
}

class _LoginScreenNDSState extends BaseLoginScreenState<LoginScreenNDS> {
  @override
  String getScreenName() => 'NDS Login Screen';

  @override
  void navigateToOtpScreen(String phoneNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreenNDS(
          phoneNumber: phoneNumber,
          phoneNumText: mobileNumberController.text.trim(),
          settings: settings,
        ),
      ),
    );
  }
}
