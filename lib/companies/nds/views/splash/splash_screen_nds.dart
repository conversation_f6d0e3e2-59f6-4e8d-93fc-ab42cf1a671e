import 'package:flutter/material.dart';
import 'package:nds_app/screens/splash_templates/splash_template1.dart';

/// NDS-specific splash screen (main splash)
/// Uses SplashTemplate1 for standard splash behavior
class SplashScreenNDS extends SplashTemplate1 {
  const SplashScreenNDS({Key? key}) : super(key: key);

  @override
  State<SplashScreenNDS> createState() => SplashScreenNDSState();
}

class SplashScreenNDSState extends SplashTemplate1State<SplashScreenNDS> {
  @override
  String getScreenName() => 'NDS Splash Screen';
}
