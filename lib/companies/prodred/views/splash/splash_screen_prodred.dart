import 'package:flutter/material.dart';
import 'package:nds_app/screens/splash_templates/splash_template1.dart';

/// ProdRed-specific splash screen (main splash)
/// Uses SplashTemplate1 for standard splash behavior
class SplashScreenProdRed extends SplashTemplate1 {
  const SplashScreenProdRed({Key? key}) : super(key: key);

  @override
  State<SplashScreenProdRed> createState() => SplashScreenProdRedState();
}

class SplashScreenProdRedState
    extends SplashTemplate1State<SplashScreenProdRed> {
  @override
  String getScreenName() => 'ProdRed Splash Screen';
}
