import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/login_templates/login_template3.dart';
import '../otp/otp_verification_screen_prodred.dart';

/// ProdRed-specific login screen
/// Uses LoginTemplate3 for customizable login UI
class LoginScreenProdRed extends LoginTemplate3 {
  const LoginScreenProdRed({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreenProdRed> createState() => _LoginScreenProdRedState();
}

class _LoginScreenProdRedState extends LoginTemplate3State<LoginScreenProdRed> {
  @override
  String getScreenName() => 'ProdRed Login Screen';

  @override
  void navigateToOtpScreen(String phoneNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreenProdRed(
          phoneNumber: phoneNumber,
          phoneNumText: mobileNumberController.text.trim(),
          settings: settings,
        ),
      ),
    );
  }

  // Example of customizing the header section for ProdRed
  @override
  Widget buildHeaderSection() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 0.01 * dimensions.height),
      child: Text(
        'Welcome to ProdRed',
        style: TextStyle(
          fontSize: 0.02 * dimensions.height,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
