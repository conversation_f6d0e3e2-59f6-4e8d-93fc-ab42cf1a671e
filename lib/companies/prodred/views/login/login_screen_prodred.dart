import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/login.dart';
import '../otp/otp_verification_screen_prodred.dart';

/// ProdRed-specific login screen
/// Extends BaseLoginScreen with ProdRed-specific navigation
class LoginScreenProdRed extends BaseLoginScreen {
  const LoginScreenProdRed({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreenProdRed> createState() => _LoginScreenProdRedState();
}

class _LoginScreenProdRedState
    extends BaseLoginScreenState<LoginScreenProdRed> {
  @override
  String getScreenName() => 'ProdRed Login Screen';

  @override
  void navigateToOtpScreen(String phoneNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreenProdRed(
          phoneNumber: phoneNumber,
          phoneNumText: mobileNumberController.text.trim(),
          settings: settings,
        ),
      ),
    );
  }
}
