import 'package:flutter/material.dart';
import 'package:nds_app/screens/splash_templates/splash_template1.dart';

/// B2C-specific splash screen (main splash)
/// Uses SplashTemplate1 for standard splash behavior
class SplashScreenB2C extends SplashTemplate1 {
  const SplashScreenB2C({Key? key}) : super(key: key);

  @override
  State<SplashScreenB2C> createState() => SplashScreenB2CState();
}

class SplashScreenB2CState extends SplashTemplate1State<SplashScreenB2C> {
  @override
  String getScreenName() => 'B2C Splash Screen';
}
