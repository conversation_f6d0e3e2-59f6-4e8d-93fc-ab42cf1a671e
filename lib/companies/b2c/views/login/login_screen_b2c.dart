import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/login.dart';
import '../otp/otp_verification_screen_b2c.dart';

/// B2C-specific login screen
/// Extends BaseLoginScreen with B2C-specific navigation
class LoginScreenB2C extends BaseLoginScreen {
  const LoginScreenB2C({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreenB2C> createState() => _LoginScreenB2CState();
}

class _LoginScreenB2CState extends BaseLoginScreenState<LoginScreenB2C> {
  @override
  String getScreenName() => 'B2C Login Screen';

  @override
  void navigateToOtpScreen(String phoneNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreenB2C(
          phoneNumber: phoneNumber,
          phoneNumText: mobileNumberController.text.trim(),
          settings: settings,
        ),
      ),
    );
  }
}
