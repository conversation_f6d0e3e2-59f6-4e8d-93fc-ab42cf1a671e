import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/login_templates/login_template1.dart';
import '../otp/otp_verification_screen_b2c.dart';

/// B2C-specific login screen
/// Uses LoginTemplate1 for standard login UI
class LoginScreenB2C extends LoginTemplate1 {
  const LoginScreenB2C({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreenB2C> createState() => _LoginScreenB2CState();
}

class _LoginScreenB2CState extends LoginTemplate1State<LoginScreenB2C> {
  @override
  String getScreenName() => 'B2C Login Screen';

  @override
  void navigateToOtpScreen(String phoneNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreenB2C(
          phoneNumber: phoneNumber,
          phoneNumText: mobileNumberController.text.trim(),
          settings: settings,
        ),
      ),
    );
  }
}
