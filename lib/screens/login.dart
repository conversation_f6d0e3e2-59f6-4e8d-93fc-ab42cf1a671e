import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/blocs/login/login_bloc.dart';
import 'package:nds_app/blocs/login/login_events.dart';
import 'package:nds_app/blocs/navigation/navigation_bar_stream.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/mobile_number_status.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/otp_verification.dart';
import 'package:nds_app/screens/splashScreen/loading_screen.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:nds_app/streams/privacy_policy_and_terms_check_box_data.dart';
import 'package:nds_app/utils/snake_bar.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:nds_app/widgets/login/privacy_policy_and_toc_check_box.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';

import '../branding/branding.dart';
import '../utils/toast.dart';

/// Base abstract class for all login screens
/// Company-specific login screens should extend this class
abstract class BaseLoginScreen extends StatefulWidget {
  final List<UserActivitySetting> settings;
  const BaseLoginScreen({
    Key? key,
    required this.settings,
  }) : super(key: key);
}

/// Base state class that provides common login functionality
/// Company-specific states should extend this class
abstract class BaseLoginScreenState<T extends BaseLoginScreen>
    extends State<T> {
  late Dimensions dimensions;
  final _bloc = LoginScreenBloc();
  final _mobileNumberController = TextEditingController();
  late List<UserActivitySetting> settings;
  NavigationBarStream navigationBarStream = NavigationBarStream();

  CheckBoxPrivacyPolicyAndTosStream stream =
      CheckBoxPrivacyPolicyAndTosStream();

  bool isPrivacyPolicyAndTocChecked = false;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': getScreenName(),
      'screen_class': widget.runtimeType.toString(),
    });

    settings = widget.settings;
    stream.checkBox.listen((event) {
      isPrivacyPolicyAndTocChecked = event;
      _bloc.eventSink
          .add(EnterNumberEvent(mobileNumber: _mobileNumberController.text));
    });
    navigationBarStream.submitIndex(0);

    super.initState();
  }

  /// Override this method to provide company-specific screen name
  String getScreenName() => 'Login Screen';

  /// Override this method to customize the login image
  Widget buildLoginImageSection() {
    return Expanded(
      child: Container(
        alignment: Alignment.center,
        child: SizedBox(
          child: Image.asset(
            "assets/login/loginScreenScooter.png",
          ),
        ),
      ),
    );
  }

  /// Override this method to customize welcome text
  Widget buildWelcomeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          loginScreen["text15"]!,
          style: urbanistTextStyle(
              0.026 * dimensions.height, loginThemeColor, FontWeight.w600),
        ),
        SizedBox(
          height: 0.01 * dimensions.height,
        ),
        Text(
          loginScreen["text16"]!,
          style: urbanistTextStyle(
              0.015 * dimensions.height, loginThemeColor, FontWeight.w400),
        ),
      ],
    );
  }

  /// Override this method to customize navigation to OTP screen
  void navigateToOtpScreen(String phoneNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreen(
          phoneNumber: phoneNumber,
          phoneNumText: _mobileNumberController.text.trim(),
          settings: settings,
        ),
      ),
    );
  }

  void fillNumber(String mobileNumber) {
    _bloc.eventSink.add(EnterNumberEvent(mobileNumber: mobileNumber));
  }

  /// Override this method to customize additional UI elements
  Widget buildAdditionalContent() {
    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    dimensions = Dimensions(context);

    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
        listener: (context, state) async {
          if (state is InternetConnectivityFailure) {
            // Show the no internet connection modal here
            WidgetsBinding.instance.addPostFrameCallback((_) {
              getBotttomNoInternetConnection(
                heading: noInternetConnectionText["text4"]!,
                context: context,
              ).then((_) {
                // Once the bottom sheet is dismissed, reset the notifier
                isBottomSheetOpenNotifier.value = false;
              });
            });
          } else if (isBottomSheetOpenNotifier.value == true &&
              state is InternetConnectivitySuccess) {
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              isBottomSheetOpenNotifier.value = false;
              Navigator.of(context).pop();
              SnackBarMessage.message(noInternetConnectionText["text5"]!,
                  backOnlineColorGreen, context);
              await Navigator.pushReplacement(
                // ignore: use_build_context_synchronously
                context,
                MaterialPageRoute(builder: (context) => const LoadingScreen()),
              );
            });
          }
        },
        child: Padding(
          padding: EdgeInsets.fromLTRB(
              0.057 * dimensions.width, 0.0, 0.057 * dimensions.width, 0.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 0.08 * dimensions.height,
              ),
              Expanded(
                child: Container(
                  alignment: Alignment.center,
                  child: SizedBox(
                    child: Image.asset(
                      "assets/login/loginScreenScooter.png",
                    ),
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    loginScreen["text15"]!,
                    style: urbanistTextStyle(0.026 * dimensions.height,
                        loginThemeColor, FontWeight.w600),
                  ),
                  SizedBox(
                    height: 0.01 * dimensions.height,
                  ),
                  Text(
                    loginScreen["text16"]!,
                    style: urbanistTextStyle(0.015 * dimensions.height,
                        loginThemeColor, FontWeight.w400),
                  ),
                ],
              ),
              SizedBox(
                height: 0.02 * dimensions.height,
              ),
              Text(
                loginScreen["text2"]!,
                style: urbanistTextStyle(0.016 * dimensions.height,
                    primaryBlueLight, FontWeight.w500),
              ),
              SizedBox(
                height: 0.0096 * dimensions.height,
              ),
              StreamBuilder<MobileNumberStatus>(
                  stream: _bloc.mobileNumberStatus,
                  initialData: MobileNumberStatus.notEntered,
                  builder: (context, snapshot) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Stack(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                  color: unSelectedTextBoxColor,
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(8.0)),
                                  border: Border.all(
                                      color: (snapshot.data ==
                                              MobileNumberStatus.notEntered
                                          ? colorBlack800
                                          : (snapshot.data ==
                                                  MobileNumberStatus.valid
                                              ? secondaryBlue
                                              : colorRed)))),
                              child: TextField(
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                      RegExp('[0-9]'))
                                ],
                                style: TextStyle(color: loginThemeColor),
                                controller: _mobileNumberController,
                                onChanged: fillNumber,
                                decoration: InputDecoration(
                                    prefixIcon: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8.0),
                                      // alignment: Alignment.centerLeft,
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            '+91',
                                            style: urbanistTextStyle(
                                                0.019 * dimensions.height,
                                                loginThemeColor,
                                                FontWeight.w400),
                                          ),
                                          const SizedBox(width: 12.0),
                                          Container(
                                            height: 0.5 *
                                                (kToolbarHeight), // 60% of the text field height
                                            width: 1.5,
                                            color: loginThemeColor,
                                          ),
                                        ],
                                      ),
                                    ),
                                    counterText: "",
                                    labelText: loginScreen["text3"]!,
                                    labelStyle: urbanistTextStyle(
                                        0.019 * dimensions.height,
                                        loginThemeColor,
                                        FontWeight.w500),
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.never,
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.fromLTRB(
                                        0.048 * dimensions.width,
                                        0.0,
                                        0.0,
                                        0.0217 * dimensions.height)),
                                maxLength: 10,
                                keyboardType:
                                    const TextInputType.numberWithOptions(
                                        signed: true, decimal: true),
                              ),
                            ),
                            Visibility(
                              visible:
                                  snapshot.data == MobileNumberStatus.invalid,
                              child: Positioned(
                                top: 0.023 * dimensions.height,
                                right: 0.023 * dimensions.width,
                                child: Image.asset(
                                  loginScreenImages["exclamationMark"]!,
                                  height: 0.025 * dimensions.height,
                                ),
                              ),
                            )
                          ],
                        ),
                        SizedBox(
                          height: 0.00488 * dimensions.height,
                        ),
                        Visibility(
                          visible: snapshot.data == MobileNumberStatus.invalid,
                          child: Text(
                            loginScreen["text6"]!,
                            style: urbanistTextStyle(0.014 * dimensions.height,
                                colorRed, FontWeight.w500),
                          ),
                        ),
                      ],
                    );
                  }),
              SizedBox(
                height: 0.005 * dimensions.height,
              ),
              CheckBoxPrivacyPolicyAndToc(
                settings: settings,
                activities: const [
                  ActivityType.privacyPolicyAcceptance,
                  ActivityType.termsConditionsAcceptance
                ],
              ),
              SizedBox(
                height: 0.04 * dimensions.height,
              ),
              // Padding(
              //   padding: EdgeInsets.only(left: 14 / 414 * dimensions.width),
              //   child: const UserConnectionTypeToggleButton(
              //     textColor: colorGrey800,
              //   ),
              // ),
              // const Expanded(
              //   child: SizedBox(),
              // ),
              StreamBuilder(
                stream: _bloc.mobileNumberStatus,
                builder: (context, snapshot) {
                  return snapshot.data == MobileNumberStatus.valid &&
                          isPrivacyPolicyAndTocChecked
                      ? GestureDetector(
                          onTap: () async {
                            getCircularProgressIndicator(context);
                            String phoneNumber =
                                "%2B91${_mobileNumberController.text.trim()}";
                            String? error =
                                await LoginService.sendLoginOtp(phoneNumber);

                            // ignore: use_build_context_synchronously
                            Navigator.pop(context);
                            if (error == null) {
                              navigateToOtpScreen(phoneNumber);
                            } else {
                              CustomToast.message(error);
                              _bloc.eventSink.add(LoginErrorEvent());
                            }
                          },
                          child: CustomButton.gesture(
                            text: loginScreen["text4"]!,
                            backgroundColor: loginThemeColor,
                            size: CustomButtonSize.fullWidth,
                            textColor: colorWhite,
                            fontWeight: FontWeight.w600,
                          ))
                      : CustomButton.gesture(
                          text: loginScreen["text4"]!,
                          backgroundColor: secondButtonColor,
                          size: CustomButtonSize.fullWidth,
                          textColor: colorWhite,
                          fontWeight: FontWeight.w600,
                        );
                },
              ),
              SizedBox(
                height: 0.01 * dimensions.height,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Default implementation for backward compatibility
class LoginScreen extends BaseLoginScreen {
  const LoginScreen({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends BaseLoginScreenState<LoginScreen> {
  // Uses all default implementations from BaseLoginScreenState
}
