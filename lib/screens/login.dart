import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';

// Export the concrete LoginScreen for backward compatibility
export 'login_templates/default_login_template.dart';

/// Base abstract class containing ONLY login business logic
/// No UI code - templates handle all UI implementation
abstract class BaseLoginScreen extends StatefulWidget {
  final List<UserActivitySetting> settings;

  const BaseLoginScreen({
    Key? key,
    required this.settings,
  }) : super(key: key);
}
