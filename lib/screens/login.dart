import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/blocs/login/login_bloc.dart';
import 'package:nds_app/blocs/login/login_events.dart';
import 'package:nds_app/blocs/navigation/navigation_bar_stream.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/otp_verification.dart';
import 'package:nds_app/screens/splashScreen/loading_screen.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/streams/privacy_policy_and_terms_check_box_data.dart';
import 'package:nds_app/utils/snake_bar.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';

import '../branding/branding.dart';
import '../companies/widgets/common/login/common_login_widgets.dart';

/// Base abstract class for all login screens
/// Company-specific login screens should extend this class
abstract class BaseLoginScreen extends StatefulWidget {
  final List<UserActivitySetting> settings;
  const BaseLoginScreen({
    Key? key,
    required this.settings,
  }) : super(key: key);
}

/// Base state class that provides common login functionality
/// Company-specific states should extend this class
abstract class BaseLoginScreenState<T extends BaseLoginScreen>
    extends State<T> {
  late Dimensions dimensions;
  final _bloc = LoginScreenBloc();
  final mobileNumberController = TextEditingController();
  late List<UserActivitySetting> settings;
  NavigationBarStream navigationBarStream = NavigationBarStream();

  CheckBoxPrivacyPolicyAndTosStream stream =
      CheckBoxPrivacyPolicyAndTosStream();

  bool isPrivacyPolicyAndTocChecked = false;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': getScreenName(),
      'screen_class': widget.runtimeType.toString(),
    });

    settings = widget.settings;
    stream.checkBox.listen((event) {
      isPrivacyPolicyAndTocChecked = event;
      _bloc.eventSink
          .add(EnterNumberEvent(mobileNumber: mobileNumberController.text));
    });
    navigationBarStream.submitIndex(0);

    super.initState();
  }

  /// Override this method to provide company-specific screen name
  String getScreenName() => 'Login Screen';

  /// Override this method to customize navigation to OTP screen
  void navigateToOtpScreen(String phoneNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreen(
          phoneNumber: phoneNumber,
          phoneNumText: mobileNumberController.text.trim(),
          settings: settings,
        ),
      ),
    );
  }
  void fillNumber(String mobileNumber) {
    _bloc.eventSink.add(EnterNumberEvent(mobileNumber: mobileNumber));
  }
  /// Override this method to customize additional UI elements
  Widget buildAdditionalContent() {
    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    dimensions = Dimensions(context);

    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
        listener: (context, state) async {
          if (state is InternetConnectivityFailure) {
            // Show the no internet connection modal here
            WidgetsBinding.instance.addPostFrameCallback((_) {
              getBotttomNoInternetConnection(
                heading: noInternetConnectionText["text4"]!,
                context: context,
              ).then((_) {
                // Once the bottom sheet is dismissed, reset the notifier
                isBottomSheetOpenNotifier.value = false;
              });
            });
          } else if (isBottomSheetOpenNotifier.value == true &&
              state is InternetConnectivitySuccess) {
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              isBottomSheetOpenNotifier.value = false;
              Navigator.of(context).pop();
              SnackBarMessage.message(noInternetConnectionText["text5"]!,
                  backOnlineColorGreen, context);
              await Navigator.pushReplacement(
                // ignore: use_build_context_synchronously
                context,
                MaterialPageRoute(builder: (context) => const LoadingScreen()),
              );
            });
          }
        },
        child: Padding(
          padding: EdgeInsets.fromLTRB(
              0.057 * dimensions.width, 0.0, 0.057 * dimensions.width, 0.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 0.08 * dimensions.height,
              ),
              CommonLoginWidgets.buildLoginImageSection(),
              CommonLoginWidgets.buildWelcomeSection(dimensions),
              SizedBox(
                height: 0.02 * dimensions.height,
              ),
              CommonLoginWidgets.buildMobileNumberSection(
                dimensions: dimensions,
                bloc: _bloc,
                mobileNumberController: mobileNumberController,
                fillNumber: fillNumber,
              ),
              SizedBox(
                height: 0.005 * dimensions.height,
              ),
              CommonLoginWidgets.buildPrivacyPolicySection(settings),
              SizedBox(
                height: 0.04 * dimensions.height,
              ),
              CommonLoginWidgets.buildLoginButton(
                bloc: _bloc,
                isPrivacyPolicyAndTocChecked: isPrivacyPolicyAndTocChecked,
                mobileNumberController: mobileNumberController,
                settings: settings,
                context: context,
                onNavigateToOtp: (phoneNumber, phoneNumText, settings) =>
                    navigateToOtpScreen(phoneNumber),
              ),
              SizedBox(
                height: 0.01 * dimensions.height,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Default implementation for backward compatibility
class LoginScreen extends BaseLoginScreen {
  const LoginScreen({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends BaseLoginScreenState<LoginScreen> {
  // Uses all default implementations from BaseLoginScreenState
}
