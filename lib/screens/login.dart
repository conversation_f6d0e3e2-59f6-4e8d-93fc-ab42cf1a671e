import 'package:flutter/material.dart';
import 'package:nds_app/blocs/login/login_bloc.dart';
import 'package:nds_app/blocs/login/login_events.dart';
import 'package:nds_app/blocs/navigation/navigation_bar_stream.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:nds_app/streams/privacy_policy_and_terms_check_box_data.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';

/// Base abstract class containing all login business logic
/// Login templates should extend this class for UI variations
abstract class BaseLoginScreen extends StatefulWidget {
  final List<UserActivitySetting> settings;

  const BaseLoginScreen({
    Key? key,
    required this.settings,
  }) : super(key: key);
}

/// Base state class that provides all login business logic
/// Templates extend this for UI variations while inheriting all business logic
abstract class BaseLoginScreenState<T extends BaseLoginScreen>
    extends State<T> {
  // Business Logic Properties
  late Dimensions dimensions;
  final bloc = LoginScreenBloc();
  final mobileNumberController = TextEditingController();
  late List<UserActivitySetting> settings;
  NavigationBarStream navigationBarStream = NavigationBarStream();
  CheckBoxPrivacyPolicyAndTosStream stream =
      CheckBoxPrivacyPolicyAndTosStream();
  bool isPrivacyPolicyAndTocChecked = false;

  // Business Logic Methods
  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': getScreenName(),
      'screen_class': widget.runtimeType.toString(),
    });

    settings = widget.settings;
    stream.checkBox.listen((event) {
      isPrivacyPolicyAndTocChecked = event;
      bloc.eventSink
          .add(EnterNumberEvent(mobileNumber: mobileNumberController.text));
    });
    navigationBarStream.submitIndex(0);

    super.initState();
  }

  @override
  void dispose() {
    mobileNumberController.dispose();
    super.dispose();
  }

  /// Business Logic: Handle mobile number input
  void fillNumber(String mobileNumber) {
    bloc.eventSink.add(EnterNumberEvent(mobileNumber: mobileNumber));
  }

  /// Business Logic: Handle login API call and navigation
  Future<void> handleLogin() async {
    getCircularProgressIndicator(context);
    String phoneNumber = "%2B91${mobileNumberController.text.trim()}";
    String? error = await LoginService.sendLoginOtp(phoneNumber);

    // ignore: use_build_context_synchronously
    Navigator.pop(context);
    if (error == null) {
      navigateToOtpScreen(phoneNumber);
    } else {
      CustomToast.message(error);
      bloc.eventSink.add(LoginErrorEvent());
    }
  }

  /// Abstract methods that templates must implement
  String getScreenName();
  void navigateToOtpScreen(String phoneNumber);

  /// Abstract method for UI - templates implement their specific UI
  @override
  Widget build(BuildContext context);
}

/// Default concrete LoginScreen for backward compatibility
/// This should not be used directly - use SplashScreenFactory.createLoginScreen() instead
class LoginScreen extends BaseLoginScreen {
  const LoginScreen({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends BaseLoginScreenState<LoginScreen> {
  @override
  String getScreenName() => 'Default Login Screen';

  @override
  void navigateToOtpScreen(String phoneNumber) {
    // This should not be used - use factory pattern instead
    throw UnimplementedError('Default LoginScreen should not be used directly. '
        'Use SplashScreenFactory.createLoginScreen() instead.');
  }

  @override
  Widget build(BuildContext context) {
    // This should not be used - use factory pattern instead
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: const [
            Icon(Icons.error, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(
              'Error: Default LoginScreen used directly',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Use SplashScreenFactory.createLoginScreen() instead',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
