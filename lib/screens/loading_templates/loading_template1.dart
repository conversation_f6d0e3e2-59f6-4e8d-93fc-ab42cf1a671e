import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/screens/loading_screen.dart';
import 'package:nds_app/services/preference_helper.dart';
import 'package:nds_app/utils/snake_bar.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:nds_app/companies/splash_screen_factory.dart';

/// LoadingTemplate1 - Standard loading screen implementation
/// Used by companies that need standard loading behavior
abstract class LoadingTemplate1 extends BaseLoadingScreen {
  const LoadingTemplate1({Key? key}) : super(key: key);
}

/// LoadingTemplate1 State - Contains all business logic and UI implementation
abstract class LoadingTemplate1State<T extends LoadingTemplate1> extends State<T> {
  
  // Business Logic Methods
  @override
  void initState() {
    super.initState();
    _checkOnboardingStatus();
  }

  void _checkOnboardingStatus() async {
    await Future.delayed(const Duration(seconds: 2));
    
    bool isOnboardingDone = await PreferenceHelper.isOnboardingDone();
    
    if (mounted) {
      if (isOnboardingDone) {
        // Navigate to login screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => SplashScreenFactory.createLoginScreen(
              settings: [], // Will be populated by login screen
            ),
          ),
        );
      } else {
        // Navigate to onboarding screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => SplashScreenFactory.createOnboardingScreen(),
          ),
        );
      }
    }
  }

  /// Abstract methods that companies must implement
  String getScreenName();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
        listener: (context, state) async {
          if (state is InternetConnectivityFailure) {
            // Show the no internet connection modal here
            WidgetsBinding.instance.addPostFrameCallback((_) {
              getBotttomNoInternetConnection(
                heading: noInternetConnectionText["text4"]!,
                context: context,
              ).then((_) {
                // Once the bottom sheet is dismissed, reset the notifier
                isBottomSheetOpenNotifier.value = false;
              });
            });
          } else if (isBottomSheetOpenNotifier.value == true &&
              state is InternetConnectivitySuccess) {
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              isBottomSheetOpenNotifier.value = false;
              Navigator.of(context).pop();
              SnackBarMessage.message(noInternetConnectionText["text5"]!,
                  backOnlineColorGreen, context);
              _checkOnboardingStatus();
            });
          }
        },
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 20),
              Text(
                'Loading...',
                style: TextStyle(fontSize: 16),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
