import 'package:flutter/material.dart';
import 'package:nds_app/screens/loading_screen.dart';

/// Default LoadingScreen for backward compatibility
/// This should not be used directly - use SplashScreenFactory.createLoadingScreen() instead
class LoadingScreen extends BaseLoadingScreen {
  const LoadingScreen({Key? key}) : super(key: key);

  @override
  State<LoadingScreen> createState() => _LoadingScreenState();
}

class _LoadingScreenState extends State<LoadingScreen> {
  String getScreenName() => 'Default Loading Screen';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: const [
            Icon(Icons.error, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(
              'Error: Default LoadingScreen used directly',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            Si<PERSON><PERSON><PERSON>(height: 8),
            Text(
              'Use SplashScreenFactory.createLoadingScreen() instead',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
