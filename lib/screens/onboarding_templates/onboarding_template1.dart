import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/onboarding_screen.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/services/preference_helper.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:nds_app/companies/splash_screen_factory.dart';

/// OnboardingTemplate1 - Standard onboarding screen implementation
/// Used by companies that need standard onboarding behavior
abstract class OnboardingTemplate1 extends BaseOnboardingScreen {
  const OnboardingTemplate1({Key? key}) : super(key: key);
}

/// OnboardingTemplate1 State - Contains all business logic and UI implementation
abstract class OnboardingTemplate1State<T extends OnboardingTemplate1> extends State<T> {
  
  // Business Logic Properties
  int currentIndex = 0;
  UserActivitySetting? privacyPolicySetting;
  UserActivitySetting? termsAndConditionsSetting;

  // Onboarding content (using centralized strings)
  List<Map<String, String>> onboardingData = [
    {
      'title': onBoardingScreenText['title1']!,
      'description': onBoardingScreenText['description1']!,
      'image': onboardingScreenImages['battery']!,
    },
    {
      'title': onBoardingScreenText['title2']!,
      'description': onBoardingScreenText['description2']!,
      'image': onboardingScreenImages['performance']!,
    }
  ];

  // Business Logic Methods
  void onNext() async {
    if (currentIndex == onboardingData.length - 1) {
      await PreferenceHelper.setOnboardingDone();
      await _navigateToLoginScreen();
    } else {
      setState(() {
        currentIndex++;
      });
    }
  }

  void onSkip() async {
    await PreferenceHelper.setOnboardingDone();
    await _navigateToLoginScreen();
  }

  Future<void> _navigateToLoginScreen() async {
    try {
      // Fetch privacy policy and terms & conditions
      final privacyPolicy = await ApiService.getPrivacyPolicy();
      final termsAndConditions = await ApiService.getTermsAndConditions();

      if (privacyPolicy != null && termsAndConditions != null) {
        privacyPolicySetting = UserActivitySetting(
            activityType: ActivityType.privacyPolicyAcceptance,
            isNewSettingExist: false,
            message: privacyPolicy.message,
            value: privacyPolicy.value);

        termsAndConditionsSetting = UserActivitySetting(
            activityType: ActivityType.termsConditionsAcceptance,
            isNewSettingExist: false,
            message: termsAndConditions.message,
            value: termsAndConditions.value);

        // Navigate to login screen
        Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (_) => SplashScreenFactory.createLoginScreen(
                      settings: [
                        privacyPolicySetting!,
                        termsAndConditionsSetting!
                      ],
                    )));
      } else {
        // If API fails, fallback to loading screen
        Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (_) => SplashScreenFactory.createLoadingScreen()));
      }
    } catch (e) {
      // If any error occurs, fallback to loading screen
      Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              builder: (_) => SplashScreenFactory.createLoadingScreen()));
    }
  }

  /// Abstract methods that companies must implement
  String getScreenName();

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(14.0),
                  child: Text(
                    onboardingData[currentIndex]['title']!,
                    style: poppinsTextStyle(24 / 414 * dimensions.width,
                        loginThemeColor, FontWeight.w900),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Text(
                    onboardingData[currentIndex]['description']!,
                    style: poppinsTextStyle(20 / 414 * dimensions.width,
                        colorBlack, FontWeight.w400),
                  ),
                ),
              ],
            ),
            Image.asset(
              onboardingData[currentIndex]['image']!,
              width: double.infinity,
            ),
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Skip Button without Background and Grey Text
                  CustomButton.text(
                    onPressed: onSkip,
                    text: "Skip",
                    foregroundColor: Colors.grey,
                  ),

                  // Next Button with White Background and Black Text
                  CustomButton.elevated(
                    onPressed: onNext,
                    text: currentIndex == onboardingData.length - 1
                        ? "Finish"
                        : "Next",
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFF3E4A5B),
                    elevation: 8,
                    borderRadius: 30,
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                  )
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            )
          ],
        ),
      ),
    );
  }
}
