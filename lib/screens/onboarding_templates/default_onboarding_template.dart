import 'package:flutter/material.dart';
import 'package:nds_app/screens/onboarding_screen.dart';

/// Default OnboardingScreen for backward compatibility
/// This should not be used directly - use SplashScreenFactory.createOnboardingScreen() instead
class OnboardingScreen extends BaseOnboardingScreen {
  const OnboardingScreen({Key? key}) : super(key: key);

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  String getScreenName() => 'Default Onboarding Screen';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: const [
            Icon(Icons.error, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(
              'Error: Default OnboardingScreen used directly',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            Sized<PERSON><PERSON>(height: 8),
            Text(
              'Use SplashScreenFactory.createOnboardingScreen() instead',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
