import 'package:flutter/material.dart';
import 'package:nds_app/screens/splash_screen.dart';

/// Default SplashScreen for backward compatibility
/// This should not be used directly - use SplashScreenFactory.createSplashScreen() instead
class SplashScreen extends BaseSplashScreen {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  String getScreenName() => 'Default Splash Screen';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: const [
            Icon(Icons.error, size: 64, color: Colors.red),
            Sized<PERSON><PERSON>(height: 16),
            Text(
              'Error: Default SplashScreen used directly',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            Sized<PERSON>ox(height: 8),
            Text(
              'Use SplashScreenFactory.createSplashScreen() instead',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
