import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/otp_screen.dart';

/// Default OtpVerificationScreen for backward compatibility
/// This should not be used directly - use company-specific OTP screens instead
class OtpVerificationScreen extends BaseOtpScreen {
  const OtpVerificationScreen({
    Key? key,
    required String phoneNumber,
    required String phoneNumText,
    required List<UserActivitySetting> settings,
  }) : super(
          key: key,
          phoneNumber: phoneNumber,
          phoneNumText: phoneNumText,
          settings: settings,
        );

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  String getScreenName() => 'Default OTP Screen';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: const [
            Icon(Icons.error, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(
              'Error: Default OtpVerificationScreen used directly',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Use company-specific OTP screens instead',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
