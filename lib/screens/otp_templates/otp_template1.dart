import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/otp/otp_bloc.dart';
import 'package:nds_app/blocs/otp/otp_events.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_event.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/otp_screen.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:nds_app/companies/dashboard_factory.dart';

/// OtpTemplate1 - Standard OTP verification screen implementation
/// Used by companies that need standard OTP behavior
abstract class OtpTemplate1 extends BaseOtpScreen {
  const OtpTemplate1({
    Key? key,
    required String phoneNumber,
    required String phoneNumText,
    required List<UserActivitySetting> settings,
  }) : super(
          key: key,
          phoneNumber: phoneNumber,
          phoneNumText: phoneNumText,
          settings: settings,
        );
}

/// OtpTemplate1 State - Contains all business logic and UI implementation
abstract class OtpTemplate1State<T extends OtpTemplate1> extends State<T> {
  
  // Business Logic Properties
  final _bloc = OtpScreenBloc();
  late List<TextEditingController> _otpContainerControllers;
  late List<FocusNode> _otpContainerFocusNodes;
  late int maxTextFieldLength;
  bool isResendEnabled = false;
  int countdown = 10;
  Timer? timer;

  // Business Logic Methods
  @override
  void initState() {
    super.initState();
    maxTextFieldLength = _bloc.streams.length;
    _otpContainerControllers = List.generate(
        maxTextFieldLength, (index) => TextEditingController());
    _otpContainerFocusNodes =
        List.generate(maxTextFieldLength, (index) => FocusNode());
    startCountdown();
  }

  @override
  void dispose() {
    timer?.cancel();
    for (var controller in _otpContainerControllers) {
      controller.dispose();
    }
    for (var focusNode in _otpContainerFocusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void startCountdown() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdown > 0) {
        setState(() {
          countdown--;
        });
      } else {
        setState(() {
          isResendEnabled = true;
        });
        timer.cancel();
      }
    });
  }

  void resendOtp() async {
    setState(() {
      isResendEnabled = false;
      countdown = 10;
    });
    startCountdown();
    
    // Resend OTP logic
    String? error = await LoginService.sendLoginOtp(widget.phoneNumber);
    if (error != null) {
      CustomToast.message(error);
    }
  }

  /// Abstract methods that companies must implement
  String getScreenName();

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: Padding(
          padding: EdgeInsets.fromLTRB(0.057 * dimensions.width,
              0.077 * dimensions.width, 0.057 * dimensions.width, 0.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 25 / 878 * dimensions.height,
              ),
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Icon(
                  Icons.arrow_back_ios,
                  color: loginThemeColor,
                ),
              ),
              SizedBox(
                height: 0.038 * dimensions.height,
              ),
              Text(
                otpVerificationScreen["text7"]!,
                style: urbanistTextStyle(20 / 414 * dimensions.width,
                    loginThemeColor, FontWeight.w700),
              ),
              SizedBox(
                height: 0.014 * dimensions.height,
              ),
              Text(
                otpVerificationScreen["text8"]!
                    .replaceFirst("@value", widget.phoneNumText),
                style: urbanistTextStyle(14 / 414 * dimensions.width,
                    otpVerificationScreenGrey, FontWeight.w500),
              ),
              SizedBox(
                height: 0.05 * dimensions.height,
              ),
              // OTP Input Fields
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: List.generate(
                  maxTextFieldLength,
                  (index) => SizedBox(
                    width: 0.12 * dimensions.width,
                    child: TextFormField(
                      controller: _otpContainerControllers[index],
                      focusNode: _otpContainerFocusNodes[index],
                      textAlign: TextAlign.center,
                      keyboardType: TextInputType.number,
                      maxLength: 1,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      decoration: InputDecoration(
                        counterText: "",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onChanged: (value) {
                        if (value.isNotEmpty && index < maxTextFieldLength - 1) {
                          _otpContainerFocusNodes[index + 1].requestFocus();
                        } else if (value.isEmpty && index > 0) {
                          _otpContainerFocusNodes[index - 1].requestFocus();
                        }
                        
                        // Update OTP stream
                        String otp = _otpContainerControllers
                            .map((controller) => controller.text)
                            .join();
                        _bloc.eventSink.add(EnterOtpEvent(otp: otp));
                      },
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 0.03 * dimensions.height,
              ),
              Row(
                children: [
                  Text(
                    otpVerificationScreen["text10"]!,
                    style: urbanistTextStyle(
                      12 / 414 * MediaQuery.of(context).size.width,
                      otpVerificationScreenGrey,
                      FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 5),
                  isResendEnabled
                      ? GestureDetector(
                          onTap: resendOtp,
                          child: Text(
                            otpVerificationScreen["text11"]!,
                            style: urbanistTextStyle(
                              12 / 414 * MediaQuery.of(context).size.width,
                              loginThemeColor,
                              FontWeight.w700,
                            ),
                          ),
                        )
                      : Text(
                          otpVerificationScreen["text9"]!
                              .replaceFirst("@value", '$countdown')
                              .toString(),
                          style: urbanistTextStyle(
                            12 / 414 * MediaQuery.of(context).size.width,
                            otpVerificationScreenGrey,
                            FontWeight.w500,
                          ),
                        ),
                ],
              ),
              const Expanded(child: SizedBox()),
              StreamBuilder<String>(
                  stream: _bloc.otp,
                  initialData: "",
                  builder: (streamContext, snapshot) {
                    return snapshot.data!.length == _bloc.streams.length
                        ? GestureDetector(
                            onTap: () async {
                              getCircularProgressIndicator(context);

                              String? errorStatusCode =
                                  await LoginService.verifyLoginOtp(
                                      snapshot.data ?? "",
                                      widget.phoneNumber);

                              // ignore: use_build_context_synchronously
                              Navigator.pop(context);
                              if (errorStatusCode == null) {
                                // ignore: use_build_context_synchronously
                                BlocProvider.of<UserVehicleBloc>(context)
                                    .add(GetUserVehicleEvent());
                                // ignore: use_build_context_synchronously
                                Navigator.pushAndRemoveUntil(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            BlocListener<UserVehicleBloc,
                                                UserVehicleState>(
                                              listener: (context, state) {},
                                              child: DashboardFactory
                                                  .createDashboard(),
                                            )),
                                    (route) => false);
                              } else {
                                CustomToast.message(errorStatusCode);
                                _bloc.eventSink.add(OtpErrorEvent());
                                // Clear all OTP fields
                                for (var controller in _otpContainerControllers) {
                                  controller.clear();
                                }
                                _otpContainerFocusNodes[0].requestFocus();
                              }
                            },
                            child: CustomButton.elevated(
                              onPressed: () {},
                              text: otpVerificationScreen["text12"]!,
                              backgroundColor: loginThemeColor,
                              foregroundColor: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                            ),
                          )
                        : CustomButton.elevated(
                            onPressed: () {},
                            text: otpVerificationScreen["text12"]!,
                            backgroundColor: otpVerificationScreenGrey,
                            foregroundColor: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                          );
                  }),
              SizedBox(
                height: 0.02 * dimensions.height,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
