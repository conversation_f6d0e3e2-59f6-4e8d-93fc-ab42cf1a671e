import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';

/// Base abstract class - completely empty
/// Templates contain all functionality (business logic + UI)
abstract class BaseOtpScreen extends StatefulWidget {
  final String phoneNumber;
  final String phoneNumText;
  final List<UserActivitySetting> settings;

  const BaseOtpScreen({
    Key? key,
    required this.phoneNumber,
    required this.phoneNumText,
    required this.settings,
  }) : super(key: key);
}

// Export the concrete OtpVerificationScreen for backward compatibility
export 'otp_templates/default_otp_template.dart';
