import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/login.dart';
import 'package:nds_app/blocs/login/login_bloc.dart';
import 'package:nds_app/blocs/login/login_events.dart';
import 'package:nds_app/blocs/navigation/navigation_bar_stream.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:nds_app/streams/privacy_policy_and_terms_check_box_data.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';
import 'package:nds_app/utils/snake_bar.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:nds_app/companies/widgets/common/login/common_login_widgets.dart';
import 'package:nds_app/companies/splash_screen_factory.dart';

/// LoginTemplate1 - Standard login UI template
/// Used by: Lapa, B2C companies
/// Features: Standard layout with image, welcome text, mobile input, privacy policy, login button
abstract class LoginTemplate1 extends BaseLoginScreen {
  const LoginTemplate1({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);
}

/// LoginTemplate1 State - Contains all business logic and UI implementation
abstract class LoginTemplate1State<T extends LoginTemplate1> extends State<T> {
  // Business Logic Properties
  late Dimensions dimensions;
  final bloc = LoginScreenBloc();
  final mobileNumberController = TextEditingController();
  late List<UserActivitySetting> settings;
  NavigationBarStream navigationBarStream = NavigationBarStream();
  CheckBoxPrivacyPolicyAndTosStream stream =
      CheckBoxPrivacyPolicyAndTosStream();
  bool isPrivacyPolicyAndTocChecked = false;

  // Business Logic Methods
  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': getScreenName(),
      'screen_class': widget.runtimeType.toString(),
    });

    settings = widget.settings;
    stream.checkBox.listen((event) {
      isPrivacyPolicyAndTocChecked = event;
      bloc.eventSink
          .add(EnterNumberEvent(mobileNumber: mobileNumberController.text));
    });
    navigationBarStream.submitIndex(0);

    super.initState();
  }

  @override
  void dispose() {
    mobileNumberController.dispose();
    super.dispose();
  }

  /// Business Logic: Handle mobile number input
  void fillNumber(String mobileNumber) {
    bloc.eventSink.add(EnterNumberEvent(mobileNumber: mobileNumber));
  }

  /// Business Logic: Handle login API call and navigation
  Future<void> handleLogin() async {
    getCircularProgressIndicator(context);
    String phoneNumber = "%2B91${mobileNumberController.text.trim()}";
    String? error = await LoginService.sendLoginOtp(phoneNumber);

    // ignore: use_build_context_synchronously
    Navigator.pop(context);
    if (error == null) {
      navigateToOtpScreen(phoneNumber);
    } else {
      CustomToast.message(error);
      bloc.eventSink.add(LoginErrorEvent());
    }
  }

  /// Abstract methods that companies must implement
  String getScreenName();
  void navigateToOtpScreen(String phoneNumber);
  @override
  Widget build(BuildContext context) {
    dimensions = Dimensions(context);

    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
        listener: (context, state) async {
          if (state is InternetConnectivityFailure) {
            // Show the no internet connection modal here
            WidgetsBinding.instance.addPostFrameCallback((_) {
              getBotttomNoInternetConnection(
                heading: noInternetConnectionText["text4"]!,
                context: context,
              ).then((_) {
                // Once the bottom sheet is dismissed, reset the notifier
                isBottomSheetOpenNotifier.value = false;
              });
            });
          } else if (isBottomSheetOpenNotifier.value == true &&
              state is InternetConnectivitySuccess) {
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              isBottomSheetOpenNotifier.value = false;
              Navigator.of(context).pop();
              SnackBarMessage.message(noInternetConnectionText["text5"]!,
                  backOnlineColorGreen, context);
              await Navigator.pushReplacement(
                // ignore: use_build_context_synchronously
                context,
                MaterialPageRoute(
                    builder: (context) =>
                        SplashScreenFactory.createLoadingScreen()),
              );
            });
          }
        },
        child: Padding(
          padding: EdgeInsets.fromLTRB(
              0.057 * dimensions.width, 0.0, 0.057 * dimensions.width, 0.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 0.08 * dimensions.height,
              ),
              // Login Image Section
              buildLoginImageSection(),
              // Welcome Text Section
              buildWelcomeSection(),
              SizedBox(
                height: 0.02 * dimensions.height,
              ),
              // Mobile Number Input Section
              buildMobileNumberSection(),
              SizedBox(
                height: 0.005 * dimensions.height,
              ),
              // Privacy Policy Section
              buildPrivacyPolicySection(),
              SizedBox(
                height: 0.04 * dimensions.height,
              ),
              // Login Button Section
              buildLoginButtonSection(),
              SizedBox(
                height: 0.01 * dimensions.height,
              ),
              // Additional content (can be overridden by companies)
              buildAdditionalContent(),
            ],
          ),
        ),
      ),
    );
  }

  /// Template UI Components - Can be overridden for customization

  Widget buildLoginImageSection() {
    return CommonLoginWidgets.buildLoginImageSection();
  }

  Widget buildWelcomeSection() {
    return CommonLoginWidgets.buildWelcomeSection(dimensions);
  }

  Widget buildMobileNumberSection() {
    return CommonLoginWidgets.buildMobileNumberSection(
      dimensions: dimensions,
      bloc: bloc,
      mobileNumberController: mobileNumberController,
      fillNumber: fillNumber,
    );
  }

  Widget buildPrivacyPolicySection() {
    return CommonLoginWidgets.buildPrivacyPolicySection(settings);
  }

  Widget buildLoginButtonSection() {
    return CommonLoginWidgets.buildLoginButton(
      bloc: bloc,
      isPrivacyPolicyAndTocChecked: isPrivacyPolicyAndTocChecked,
      mobileNumberController: mobileNumberController,
      settings: settings,
      context: context,
      onNavigateToOtp: (phoneNumber, phoneNumText, settings) =>
          navigateToOtpScreen(phoneNumber),
    );
  }

  /// Override this method to add additional UI elements
  Widget buildAdditionalContent() {
    return const SizedBox.shrink();
  }
}
