import 'package:flutter/material.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/login.dart';

/// Default LoginScreen for backward compatibility
/// This should not be used directly - use SplashScreenFactory.createLoginScreen() instead
class LoginScreen extends BaseLoginScreen {
  const LoginScreen({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends BaseLoginScreenState<LoginScreen> {
  @override
  String getScreenName() => 'Default Login Screen';

  @override
  void navigateToOtpScreen(String phoneNumber) {
    // This should not be used - use factory pattern instead
    throw UnimplementedError(
      'Default LoginScreen should not be used directly. '
      'Use SplashScreenFactory.createLoginScreen() instead.'
    );
  }

  @override
  Widget build(BuildContext context) {
    dimensions = Dimensions(context);

    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: const [
            Icon(Icons.error, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(
              'Error: Default LoginScreen used directly',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Use SplashScreenFactory.createLoginScreen() instead',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
