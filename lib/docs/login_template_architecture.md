# Login Template Architecture

## Overview
This document describes the new template-based architecture for login screens that provides better maintainability and scalability for multi-company applications.

## Architecture Components

### 1. BaseLoginScreen (Business Logic Layer)
**Location**: `lib/screens/login.dart`

Contains all shared business logic:
- API calls and authentication
- Form validation
- Navigation logic
- State management
- Error handling
- Analytics tracking

```dart
abstract class BaseLoginScreen extends StatefulWidget {
  final List<UserActivitySetting> settings;
  // Abstract class - no UI implementation
}

abstract class BaseLoginScreenState<T extends BaseLoginScreen> extends State<T> {
  // All business logic properties and methods
  late Dimensions dimensions;
  final bloc = LoginScreenBloc();
  final mobileNumberController = TextEditingController();
  // ... business logic methods
}
```

### 2. Login Templates (UI Layer)
**Location**: `lib/screens/login_templates/`

#### LoginTemplate1 - Standard UI
**Used by**: Lapa, B2C
**Features**: Standard layout with image, welcome text, mobile input, privacy policy, login button

#### LoginTemplate2 - Alternative UI  
**Used by**: NDS, Nichesolv
**Features**: Same as Template1 but can be customized for different UI variations

#### LoginTemplate3 - Customizable UI
**Used by**: ProdRed
**Features**: Based on Template2 with additional customization options (header, footer sections)

### 3. Company-Specific Screens (Implementation Layer)
**Location**: `lib/companies/[company]/views/login/`

Each company screen:
- Extends the appropriate template
- Implements company-specific navigation (OTP screen routing)
- Can override UI components for customization

## Template Mapping

| Company | Template | Reason |
|---------|----------|---------|
| Lapa | LoginTemplate1 | Standard UI requirements |
| B2C | LoginTemplate1 | Same UI as Lapa |
| NDS | LoginTemplate2 | Alternative UI variation |
| Nichesolv | LoginTemplate2 | Same UI as NDS |
| ProdRed | LoginTemplate3 | Custom header/footer needs |

## Benefits

### 1. Single Source of Truth for Business Logic
- All API calls, validation, and business rules in BaseLoginScreen
- Bug fixes automatically apply to all companies
- Consistent behavior across all login screens

### 2. UI Template Reuse
- Multiple companies can share the same UI template
- Updates to a template automatically reflect for all companies using it
- Reduces UI code duplication by ~80%

### 3. Easy Customization
- Companies can override specific UI components
- Templates provide extension points for customization
- Maintains consistency while allowing flexibility

### 4. Scalability
- Adding new companies requires minimal code
- New templates can be created for different UI needs
- Factory pattern handles instantiation automatically

## Implementation Example

### Company Screen Implementation
```dart
// Lapa uses LoginTemplate1
class LoginScreenLapa extends LoginTemplate1 {
  const LoginScreenLapa({Key? key, required List<UserActivitySetting> settings})
      : super(key: key, settings: settings);

  @override
  State<LoginScreenLapa> createState() => _LoginScreenLapaState();
}

class _LoginScreenLapaState extends LoginTemplate1State<LoginScreenLapa> {
  @override
  String getScreenName() => 'Lapa Login Screen';

  @override
  void navigateToOtpScreen(String phoneNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreenLapa(
          phoneNumber: phoneNumber,
          phoneNumText: mobileNumberController.text.trim(),
          settings: settings,
        ),
      ),
    );
  }
}
```

### Template Customization Example
```dart
// ProdRed customizes LoginTemplate3
class _LoginScreenProdRedState extends LoginTemplate3State<LoginScreenProdRed> {
  @override
  Widget buildHeaderSection() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 0.01 * dimensions.height),
      child: Text(
        'Welcome to ProdRed',
        style: TextStyle(
          fontSize: 0.02 * dimensions.height,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
```

## File Structure
```
lib/
├── screens/
│   ├── login.dart                    # BaseLoginScreen (business logic)
│   └── login_templates/
│       ├── login_template1.dart      # Standard UI template
│       ├── login_template2.dart      # Alternative UI template
│       └── login_template3.dart      # Customizable UI template
├── companies/
│   ├── splash_screen_factory.dart    # Factory for instantiation
│   ├── b2c/views/login/login_screen_b2c.dart
│   ├── lapa/views/login/login_screen_lapa.dart
│   ├── nds/views/login/login_screen_nds.dart
│   ├── nichesolv/views/login/login_screen_nichesolv.dart
│   └── prodred/views/login/login_screen_prodred.dart
└── docs/
    └── login_template_architecture.md # This document
```

## Migration Benefits

### Before (Factory Pattern)
- Business logic duplicated across all company screens
- UI code duplicated across all company screens
- Changes required updates to multiple files
- High maintenance overhead

### After (Template Pattern)
- Business logic centralized in BaseLoginScreen
- UI templates shared across companies
- Changes to templates automatically propagate
- Low maintenance overhead
- Easy to add new companies or templates

## Future Extensions

This template pattern can be extended to other screens:
- OTP verification screens
- Dashboard screens
- Profile screens
- Any screen with shared business logic but different UI needs

## Usage

The factory continues to work as before:
```dart
Widget loginScreen = SplashScreenFactory.createLoginScreen(settings: userSettings);
```

Companies automatically get the appropriate template based on their configuration.
